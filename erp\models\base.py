"""
Base model with database integration
"""
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List, Type, Union
from ..fields import Field
from ..database.registry import DatabaseRegistry


class ModelMeta(type):
    """Metaclass for models to handle field definitions"""
    
    def __new__(cls, name, bases, attrs):
        # Collect fields from the class and its parents
        fields = {}
        
        # Get fields from parent classes
        for base in bases:
            if hasattr(base, '_fields'):
                fields.update(base._fields)
        
        # Get fields from current class
        for key, value in list(attrs.items()):
            if isinstance(value, Field):
                fields[key] = value
                # Remove field from class attributes to avoid conflicts
                del attrs[key]
        
        # Store fields in the class
        attrs['_fields'] = fields
        
        # Create the class
        new_class = super().__new__(cls, name, bases, attrs)
        
        # Register the model
        if hasattr(new_class, '_name') and new_class._name:
            ModelRegistry.register(new_class._name, new_class)
        
        return new_class


class ModelRegistry:
    """Registry to store all model classes"""
    _models = {}
    
    @classmethod
    def register(cls, name: str, model_class: Type):
        cls._models[name] = model_class
    
    @classmethod
    def get(cls, name: str) -> Optional[Type]:
        return cls._models.get(name)
    
    @classmethod
    def all(cls) -> Dict[str, Type]:
        return cls._models.copy()


class BaseModel(metaclass=ModelMeta):
    """Base model class with database integration"""
    
    _name = None  # Model name (to be overridden in subclasses)
    _description = None  # Model description
    _table = None  # Database table name
    
    # Common fields for all models
    from ..fields import Char, Datetime
    id = Char(string='ID', required=True, readonly=True, default=lambda: str(uuid.uuid4()))
    name = Char(string='Name', required=True)
    createAt = Datetime(string='Created At', readonly=True, default=lambda: datetime.now())
    updateAt = Datetime(string='Updated At', readonly=True, default=lambda: datetime.now())
    
    def __init__(self, **kwargs):
        self._values = {}
        self._is_new_record = True
        
        # Initialize fields with default values
        for field_name, field in self._fields.items():
            if field_name in kwargs:
                self._values[field_name] = kwargs[field_name]
            else:
                default_value = field.get_default_value()
                if default_value is not None:
                    self._values[field_name] = default_value
    
    def __getattr__(self, name):
        """Get field value"""
        if name in self._fields:
            return self._values.get(name)
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")
    
    def __setattr__(self, name, value):
        """Set field value"""
        if name.startswith('_') or name in ('_fields', '_name', '_description', '_table'):
            super().__setattr__(name, value)
        elif hasattr(self, '_fields') and name in self._fields:
            field = self._fields[name]
            if field.readonly and not self._is_new_record:
                raise ValueError(f"Field '{name}' is readonly")
            validated_value = field.validate(value)
            self._values[name] = validated_value
        else:
            super().__setattr__(name, value)
    
    @classmethod
    async def create(cls, vals: Dict[str, Any]):
        """Create a new record in database"""
        record = cls(**vals)
        
        # Get database manager
        db = await DatabaseRegistry.get_current_database()
        if not db:
            raise RuntimeError("No database connection available")
        
        # Prepare data for insertion
        insert_data = {}
        for field_name, field in cls._fields.items():
            if field_name in record._values:
                insert_data[field_name] = record._values[field_name]
        
        # Insert into database
        table_name = cls._table or cls._name.replace('.', '_')
        record_id = await db.insert(table_name, insert_data)
        
        if record_id:
            record._values['id'] = str(record_id)
        
        record._is_new_record = False
        return record
    
    @classmethod
    async def search(cls, domain: List = None, limit: int = None, offset: int = 0, order: str = None):
        """Search records matching domain"""
        db = await DatabaseRegistry.get_current_database()
        if not db:
            raise RuntimeError("No database connection available")
        
        table_name = cls._table or cls._name.replace('.', '_')
        
        # Build query
        query = f"SELECT * FROM {table_name}"
        params = []
        
        # Add WHERE clause for domain
        if domain:
            where_conditions = []
            for condition in domain:
                if len(condition) == 3:
                    field, operator, value = condition
                    if operator == '=':
                        where_conditions.append(f"{field} = ${len(params) + 1}")
                        params.append(value)
                    elif operator == '!=':
                        where_conditions.append(f"{field} != ${len(params) + 1}")
                        params.append(value)
                    elif operator == 'like':
                        where_conditions.append(f"{field} LIKE ${len(params) + 1}")
                        params.append(value)
                    elif operator == 'ilike':
                        where_conditions.append(f"{field} ILIKE ${len(params) + 1}")
                        params.append(value)
                    elif operator == 'in':
                        placeholders = ','.join([f"${len(params) + i + 1}" for i in range(len(value))])
                        where_conditions.append(f"{field} IN ({placeholders})")
                        params.extend(value)
                    elif operator == '>':
                        where_conditions.append(f"{field} > ${len(params) + 1}")
                        params.append(value)
                    elif operator == '<':
                        where_conditions.append(f"{field} < ${len(params) + 1}")
                        params.append(value)
                    elif operator == '>=':
                        where_conditions.append(f"{field} >= ${len(params) + 1}")
                        params.append(value)
                    elif operator == '<=':
                        where_conditions.append(f"{field} <= ${len(params) + 1}")
                        params.append(value)
            
            if where_conditions:
                query += " WHERE " + " AND ".join(where_conditions)
        
        # Add ORDER BY clause
        if order:
            query += f" ORDER BY {order}"
        
        # Add LIMIT and OFFSET
        if limit:
            query += f" LIMIT {limit}"
        if offset:
            query += f" OFFSET {offset}"
        
        # Execute query
        rows = await db.fetch(query, *params)
        
        # Convert rows to model instances
        records = []
        for row in rows:
            record = cls()
            record._values = dict(row)
            record._is_new_record = False
            records.append(record)
        
        return records
    
    @classmethod
    async def browse(cls, ids: Union[str, List[str]]):
        """Browse records by IDs"""
        if isinstance(ids, str):
            ids = [ids]
        
        if not ids:
            return []
        
        return await cls.search([('id', 'in', ids)])
    
    async def write(self, vals: Dict[str, Any]):
        """Update record with new values"""
        if self._is_new_record:
            raise ValueError("Cannot update a new record. Use create() instead.")
        
        # Update values
        for field_name, value in vals.items():
            if field_name in self._fields:
                setattr(self, field_name, value)
        
        # Update updateAt timestamp
        if 'updateAt' in self._fields:
            self._values['updateAt'] = datetime.now()
        
        # Save to database
        db = await DatabaseRegistry.get_current_database()
        if not db:
            raise RuntimeError("No database connection available")
        
        table_name = self._table or self._name.replace('.', '_')
        record_id = self._values.get('id')
        
        if not record_id:
            raise ValueError("Record has no ID for update")
        
        # Prepare update data
        update_data = {}
        for field_name, value in vals.items():
            if field_name in self._fields:
                update_data[field_name] = self._values[field_name]
        
        # Add updateAt if it exists
        if 'updateAt' in self._fields:
            update_data['updateAt'] = self._values['updateAt']
        
        await db.update(table_name, record_id, update_data)
        return True
    
    async def unlink(self):
        """Delete record from database"""
        if self._is_new_record:
            raise ValueError("Cannot delete a new record")
        
        db = await DatabaseRegistry.get_current_database()
        if not db:
            raise RuntimeError("No database connection available")
        
        table_name = self._table or self._name.replace('.', '_')
        record_id = self._values.get('id')
        
        if not record_id:
            raise ValueError("Record has no ID for deletion")
        
        await db.delete(table_name, record_id)
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert record to dictionary"""
        return self._values.copy()
    
    def __repr__(self):
        name = self._values.get('name', 'Unknown')
        record_id = self._values.get('id', 'New')
        return f"<{self.__class__.__name__}({record_id}): {name}>"
